import { NavLink } from "react-router-dom";
import { cn } from "@/lib/utils";

function Nav() {
  return (
    <nav className="flex justify-center mb-4 md:mb-6 bg-gray-100 p-2.5 rounded-lg max-w-fit mx-auto">
      <NavLink
        to="/"
        className={({ isActive }) => cn(
          "mx-1 md:mx-4 no-underline text-gray-800 font-medium py-2 px-2 md:px-3 rounded-md transition-colors duration-300 hover:bg-gray-200 text-sm md:text-base",
          isActive && "bg-blue-600 text-white hover:bg-blue-700"
        )}
      >
        图像编辑 (新版)
      </NavLink>
      <NavLink
        to="/try-on"
        className={({ isActive }) => cn(
          "mx-1 md:mx-4 no-underline text-gray-800 font-medium py-2 px-2 md:px-3 rounded-md transition-colors duration-300 hover:bg-gray-200 text-sm md:text-base",
          isActive && "bg-blue-600 text-white hover:bg-blue-700"
        )}
      >
        虚拟试衣
      </NavLink>
      <NavLink
        to="/settings"
        className={({ isActive }) => cn(
          "mx-1 md:mx-4 no-underline text-gray-800 font-medium py-2 px-2 md:px-3 rounded-md transition-colors duration-300 hover:bg-gray-200 text-sm md:text-base",
          isActive && "bg-blue-600 text-white hover:bg-blue-700"
        )}
      >
        系统设置
      </NavLink>
    </nav>
  );
}

export default Nav;
