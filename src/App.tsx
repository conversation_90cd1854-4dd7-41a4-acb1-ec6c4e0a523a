import { BrowserRouter as Router, Routes, Route } from "react-router-dom";
import SettingsPage from "./pages/Settings";
import TryOnPage from "./pages/TryOn";
import ImageEditPage from "./pages/ImageEdit";
import ImageEditMigratedPage from "./pages/ImageEditMigrated";
import Nav from "./components/Nav";
import { TestComponent } from "./components/TestComponent";

function App() {
  return (
    <Router>
      <main className="max-w-4xl mx-auto p-8">
        <Nav />
        <Routes>
          <Route path="/" element={<ImageEditMigratedPage />} />
          <Route path="/image-edit" element={<ImageEditPage />} />
          <Route path="/image-edit-new" element={<ImageEditMigratedPage />} />
          <Route path="/try-on" element={<TryOnPage />} />
          <Route path="/settings" element={<SettingsPage />} />
          <Route path="/test" element={<TestComponent />} />
        </Routes>
      </main>
    </Router>
  );
}

export default App;
