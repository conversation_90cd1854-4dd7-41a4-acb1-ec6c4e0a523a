import { useState } from "react";
import { <PERSON>, Cloud, Sliders, Save } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";

function SettingsMigratedPage() {
  // 图像编辑参数配置
  const [editStrength, setEditStrength] = useState<number>(0.7);
  const [guidanceScale, setGuidanceScale] = useState<number>(7.5);
  const [inferenceSteps, setInferenceSteps] = useState<number>(20);
  const [randomSeed, setRandomSeed] = useState<string>("");

  // 硬编码的API密钥信息（仅用于显示配置状态）
  const douBaoApiKey = "2e3e1b1f-b6fb-46c7-a519-343d3146b88f"; // 豆包API密钥
  const aliyunApiKey = "sk-52033e5a00504594befe248ad868a029"; // 阿里云API密钥

  // 硬编码的腾讯云COS配置信息（仅用于显示配置状态）
  const cosRegion = "ap-shanghai";
  const cosBucketName = "tennis-1258507500";

  // 保存设置的函数
  const saveSettings = () => {
    // 这里可以添加保存设置到本地存储或后端的逻辑
    alert("设置已保存！");
  };

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">系统设置</h1>
        <p className="text-gray-600">配置AI图像处理工具的参数设置</p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* API配置状态 */}
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Key className="h-5 w-5" />
                API配置状态
              </CardTitle>
              <CardDescription>
                查看当前API密钥配置状态
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* 豆包API配置 */}
              <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                <p className="text-green-700 font-medium">✅ 豆包API密钥已配置</p>
                <p className="text-xs text-green-600 mt-1">
                  密钥: {douBaoApiKey.substring(0, 8)}...{douBaoApiKey.substring(douBaoApiKey.length - 8)}
                </p>
              </div>

              {/* 阿里云API配置 */}
              <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                <p className="text-green-700 font-medium">✅ 阿里云API密钥已配置</p>
                <p className="text-xs text-green-600 mt-1">
                  密钥: {aliyunApiKey.substring(0, 8)}...{aliyunApiKey.substring(aliyunApiKey.length - 8)}
                </p>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Cloud className="h-5 w-5" />
                云存储配置
              </CardTitle>
              <CardDescription>
                腾讯云COS存储配置状态
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                <p className="text-green-700 font-medium">✅ 腾讯云COS配置已设置</p>
                <p className="text-xs text-green-600 mt-1">
                  区域: {cosRegion} | 存储桶: {cosBucketName}
                </p>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* 参数配置 */}
        <div>
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Sliders className="h-5 w-5" />
                图像编辑参数
              </CardTitle>
              <CardDescription>
                调整AI图像处理的参数设置
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-2">
                <Label htmlFor="edit-strength">编辑强度: {editStrength}</Label>
                <input
                  type="range"
                  id="edit-strength"
                  min="0.1"
                  max="1.0"
                  step="0.1"
                  value={editStrength}
                  onChange={(e) => setEditStrength(parseFloat(e.target.value))}
                  className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                />
                <p className="text-xs text-gray-500">控制AI编辑的强度，值越高编辑效果越明显</p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="guidance-scale">引导比例: {guidanceScale}</Label>
                <input
                  type="range"
                  id="guidance-scale"
                  min="1"
                  max="20"
                  step="0.5"
                  value={guidanceScale}
                  onChange={(e) => setGuidanceScale(parseFloat(e.target.value))}
                  className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                />
                <p className="text-xs text-gray-500">控制AI对提示词的遵循程度，值越高越严格遵循提示</p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="inference-steps">推理步数: {inferenceSteps}</Label>
                <input
                  type="range"
                  id="inference-steps"
                  min="10"
                  max="50"
                  step="1"
                  value={inferenceSteps}
                  onChange={(e) => setInferenceSteps(parseInt(e.target.value))}
                  className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                />
                <p className="text-xs text-gray-500">AI推理的步数，步数越多质量越高但处理时间越长</p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="random-seed">随机种子 (可选)</Label>
                <Input
                  id="random-seed"
                  type="text"
                  placeholder="留空使用随机种子，或输入数字固定种子"
                  value={randomSeed}
                  onChange={(e) => setRandomSeed(e.target.value)}
                />
                <p className="text-xs text-gray-500">固定种子可以获得可重复的结果</p>
              </div>

              <Button className="w-full" onClick={saveSettings}>
                <Save className="mr-2 h-4 w-4" />
                保存设置
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* 使用说明 */}
      <Card>
        <CardHeader>
          <CardTitle>使用说明</CardTitle>
          <CardDescription>
            功能说明和参数调整建议
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <h3 className="font-semibold mb-2">功能说明：</h3>
            <ul className="space-y-1 text-sm text-gray-600">
              <li className="flex items-center gap-2">
                <span className="w-2 h-2 bg-blue-500 rounded-full"></span>
                <strong>图像编辑</strong>：使用豆包API进行AI图像编辑和增强
              </li>
              <li className="flex items-center gap-2">
                <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                <strong>虚拟试衣</strong>：使用阿里云API实现AI虚拟试衣功能
              </li>
              <li className="flex items-center gap-2">
                <span className="w-2 h-2 bg-purple-500 rounded-full"></span>
                <strong>云存储</strong>：使用腾讯云COS存储和管理图片文件
              </li>
            </ul>
          </div>
          
          <div>
            <h3 className="font-semibold mb-2">参数调整建议：</h3>
            <ul className="space-y-1 text-sm text-gray-600">
              <li><strong>编辑强度</strong>：建议从0.5开始调整，根据效果增减</li>
              <li><strong>引导比例</strong>：一般设置为7-12，过高可能导致过度拟合</li>
              <li><strong>推理步数</strong>：20-30步通常能获得较好的质量平衡</li>
              <li><strong>随机种子</strong>：用于复现特定效果，留空则每次随机</li>
            </ul>
          </div>
          
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h3 className="font-semibold mb-2 text-blue-800">注意事项：</h3>
            <ul className="space-y-1 text-sm text-blue-700">
              <li>• 所有API密钥已预配置，无需手动输入</li>
              <li>• 参数调整会影响处理时间和效果质量</li>
              <li>• 建议在测试时使用较低参数，确认效果后再提高</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

export default SettingsMigratedPage;
