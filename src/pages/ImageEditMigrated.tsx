import { useState } from "react";
import { Upload, Image, Loader2, Download, Settings, Search } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";

function ImageEditMigratedPage() {
  const [selectedFile, setSelectedFile] = useState<string>("");
  const [prompt, setPrompt] = useState<string>("");
  const [isUploading] = useState<boolean>(false);
  const [isProcessing] = useState<boolean>(false);
  const [uploadedUrl] = useState<string>("");
  const [taskId] = useState<string>("");
  const [resultImages] = useState<string[]>([]);
  const [isQuerying] = useState(false);
  const [error, setError] = useState<string>("");
  const [progress] = useState<string>("");

  // 图像编辑参数
  const [editStrength, setEditStrength] = useState<number>(0.7);
  const [guidanceScale, setGuidanceScale] = useState<number>(7.5);

  // 手动输入的图片URL
  const [manualImageUrl, setManualImageUrl] = useState<string>("");

  const handleFilePathChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSelectedFile(event.target.value);
    setError("");
  };

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">AI 图像编辑</h1>
        <p className="text-gray-600">使用 AI 技术编辑和增强您的图像</p>
      </div>

      {/* 错误消息 */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <p className="text-red-700">{error}</p>
        </div>
      )}

      {/* 进度消息 */}
      {progress && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 flex items-center gap-3">
          <Loader2 className="h-5 w-5 animate-spin text-blue-600" />
          <p className="text-blue-700">{progress}</p>
        </div>
      )}

      <div className="grid grid-cols-1 xl:grid-cols-2 gap-4 md:gap-6">
        {/* 配置面板 */}
        <div className="space-y-4 md:space-y-6">
          {/* 系统配置 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5" />
                系统配置
              </CardTitle>
              <CardDescription>
                配置 API 密钥和云存储设置
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-3 md:space-y-4">
              <div className="space-y-2">
                <Label htmlFor="api-key">豆包 API 密钥</Label>
                <Input
                  id="api-key"
                  type="password"
                  value="2e3e1b1f-b6fb-46c7-a519-343d3146b88f"
                  disabled
                  className="bg-gray-50"
                />
                <p className="text-xs text-gray-500">API 密钥已预配置</p>
              </div>
            </CardContent>
          </Card>

          {/* 图片上传 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Upload className="h-5 w-5" />
                图片上传
              </CardTitle>
              <CardDescription>
                选择本地图片或输入图片URL
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="file-path">本地文件路径</Label>
                <Input
                  id="file-path"
                  type="text"
                  placeholder="请输入图片文件路径"
                  value={selectedFile}
                  onChange={handleFilePathChange}
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="image-url">或输入图片URL</Label>
                <Input
                  id="image-url"
                  type="url"
                  placeholder="https://example.com/image.jpg"
                  value={manualImageUrl}
                  onChange={(e) => setManualImageUrl(e.target.value)}
                />
              </div>

              <Button 
                className="w-full" 
                disabled={isUploading || (!selectedFile && !manualImageUrl)}
              >
                {isUploading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    上传中...
                  </>
                ) : (
                  <>
                    <Upload className="mr-2 h-4 w-4" />
                    上传图片
                  </>
                )}
              </Button>
            </CardContent>
          </Card>

          {/* 编辑参数 */}
          <Card>
            <CardHeader>
              <CardTitle>编辑参数</CardTitle>
              <CardDescription>
                调整 AI 图像编辑的参数
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-3 md:space-y-4">
              <div className="space-y-2">
                <Label htmlFor="prompt">编辑提示词</Label>
                <textarea
                  id="prompt"
                  className="flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                  placeholder="描述您想要的编辑效果..."
                  value={prompt}
                  onChange={(e) => setPrompt(e.target.value)}
                />
              </div>

              <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 md:gap-4">
                <div className="space-y-2">
                  <Label htmlFor="edit-strength">编辑强度: {editStrength}</Label>
                  <input
                    id="edit-strength"
                    type="range"
                    min="0.1"
                    max="1.0"
                    step="0.1"
                    value={editStrength}
                    onChange={(e) => setEditStrength(parseFloat(e.target.value))}
                    className="w-full"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="guidance-scale">引导比例: {guidanceScale}</Label>
                  <input
                    id="guidance-scale"
                    type="range"
                    min="1"
                    max="20"
                    step="0.5"
                    value={guidanceScale}
                    onChange={(e) => setGuidanceScale(parseFloat(e.target.value))}
                    className="w-full"
                  />
                </div>
              </div>

              <Button 
                className="w-full" 
                disabled={isProcessing || !uploadedUrl || !prompt}
              >
                {isProcessing ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    处理中...
                  </>
                ) : (
                  <>
                    <Image className="mr-2 h-4 w-4" />
                    开始编辑
                  </>
                )}
              </Button>
            </CardContent>
          </Card>
        </div>

        {/* 结果面板 */}
        <div className="xl:row-span-2">
          <Card className="h-full min-h-[600px] xl:min-h-[800px]">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Image className="h-5 w-5" />
                编辑结果
              </CardTitle>
              <CardDescription>
                AI 编辑后的图像将显示在这里
              </CardDescription>
            </CardHeader>
            <CardContent className="h-full">
              {resultImages.length > 0 ? (
                <div className="space-y-4 h-full">
                  {resultImages.map((imageUrl, index) => (
                    <div key={index} className="space-y-2">
                      <img
                        src={imageUrl}
                        alt={`编辑结果 ${index + 1}`}
                        className="w-full h-auto max-h-[500px] xl:max-h-[600px] object-contain rounded-lg border"
                      />
                      <div className="flex gap-2">
                        <Button size="sm" variant="outline">
                          <Download className="mr-2 h-4 w-4" />
                          下载
                        </Button>
                        <Button size="sm" variant="outline">
                          <Search className="mr-2 h-4 w-4" />
                          查看详情
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="flex flex-col items-center justify-center h-full min-h-[400px] xl:min-h-[600px] text-gray-500">
                  <Image className="h-16 w-16 xl:h-20 xl:w-20 mb-6 opacity-50" />
                  <p className="text-xl xl:text-2xl font-medium mb-2">暂无编辑结果</p>
                  <p className="text-sm xl:text-base text-center max-w-sm">上传图片并开始编辑以查看结果</p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>

      {/* 任务信息 */}
      {taskId && (
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium">任务ID: {taskId}</p>
                <p className="text-xs text-gray-500">您可以使用此ID查询任务状态</p>
              </div>
              <Button size="sm" variant="outline" disabled={isQuerying}>
                {isQuerying ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    查询中...
                  </>
                ) : (
                  <>
                    <Search className="mr-2 h-4 w-4" />
                    查询状态
                  </>
                )}
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}

export default ImageEditMigratedPage;
