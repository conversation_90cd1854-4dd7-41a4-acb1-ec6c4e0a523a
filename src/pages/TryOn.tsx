import { useState } from "react";
import { invoke } from "@tauri-apps/api/core";
import { Loader2, Image, Upload, Sparkles, Download } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";

function TryOnPage() {
  const [modelImageUrl, setModelImageUrl] = useState("");
  const [garmentImageUrl, setGarmentImageUrl] = useState("");
  const [isProcessing, setIsProcessing] = useState(false);
  const [resultImageUrl, setResultImageUrl] = useState("");
  const [error, setError] = useState("");

  // 阿里云API密钥 - 硬编码配置，不再通过用户输入获取
  // 密钥已从前端界面隐藏，直接在代码中设置
  const aliyunApiKey = "sk-52033e5a00504594befe248ad868a029";

  const handleStartTryOn = async () => {
    // API密钥已硬编码，只需检查图片URL是否提供
    if (!modelImageUrl || !garmentImageUrl) {
      setError("请提供模特图片URL和服装图片URL");
      return;
    }

    setIsProcessing(true);
    setError("");
    setResultImageUrl("");

    try {
      const result: any = await invoke("start_aliyun_tryon", {
        apiKey: aliyunApiKey,
        imageUrl: modelImageUrl,
        garmentUrl: garmentImageUrl,
      });

      if (result.output.task_status === "SUCCEEDED" && result.output.image_url) {
        setResultImageUrl(result.output.image_url);
      } else {
        setError(`处理失败: ${result.output.task_status}`);
      }
    } catch (err) {
      setError(`发生错误: ${err}`);
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <div className="space-y-4 md:space-y-6 w-full">
      <div className="text-center">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">AI 虚拟试衣</h1>
        <p className="text-gray-600">提供模特图片和服装图片，查看AI虚拟试衣效果</p>
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <p className="text-red-700">错误: {error}</p>
        </div>
      )}

      <div className="grid grid-cols-1 xl:grid-cols-2 gap-4 md:gap-6">
        {/* 配置面板 */}
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Upload className="h-5 w-5" />
                图片配置
              </CardTitle>
              <CardDescription>
                上传或输入模特图片和服装图片的URL
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="model-image-url">模特图片URL</Label>
                <Input
                  id="model-image-url"
                  type="url"
                  placeholder="https://example.com/model.jpg"
                  value={modelImageUrl}
                  onChange={(e) => setModelImageUrl(e.target.value)}
                />
                {modelImageUrl && (
                  <div className="mt-2">
                    <img
                      src={modelImageUrl}
                      alt="模特图片预览"
                      className="w-full h-32 object-cover rounded-md border"
                      onError={(e) => {
                        e.currentTarget.style.display = 'none';
                      }}
                    />
                  </div>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="garment-image-url">服装图片URL</Label>
                <Input
                  id="garment-image-url"
                  type="url"
                  placeholder="https://example.com/garment.jpg"
                  value={garmentImageUrl}
                  onChange={(e) => setGarmentImageUrl(e.target.value)}
                />
                {garmentImageUrl && (
                  <div className="mt-2">
                    <img
                      src={garmentImageUrl}
                      alt="服装图片预览"
                      className="w-full h-32 object-cover rounded-md border"
                      onError={(e) => {
                        e.currentTarget.style.display = 'none';
                      }}
                    />
                  </div>
                )}
              </div>

              <Button
                className="w-full"
                onClick={handleStartTryOn}
                disabled={isProcessing || !modelImageUrl || !garmentImageUrl}
              >
                {isProcessing ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    处理中...
                  </>
                ) : (
                  <>
                    <Sparkles className="mr-2 h-4 w-4" />
                    开始AI虚拟试衣
                  </>
                )}
              </Button>
            </CardContent>
          </Card>

          {/* API 配置状态 */}
          <Card>
            <CardHeader>
              <CardTitle>API 配置状态</CardTitle>
              <CardDescription>
                阿里云API密钥配置状态
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                <p className="text-green-700 font-medium">✅ 阿里云API密钥已配置</p>
                <p className="text-xs text-green-600 mt-1">
                  密钥: {aliyunApiKey.substring(0, 8)}...{aliyunApiKey.substring(aliyunApiKey.length - 8)}
                </p>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* 结果面板 */}
        <div className="xl:row-span-2">
          <Card className="h-full min-h-[600px] xl:min-h-[800px]">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Image className="h-5 w-5" />
                试衣结果
              </CardTitle>
              <CardDescription>
                AI 虚拟试衣结果将显示在这里
              </CardDescription>
            </CardHeader>
            <CardContent className="h-full">
              {resultImageUrl ? (
                <div className="space-y-4 h-full">
                  <img
                    src={resultImageUrl}
                    alt="虚拟试衣结果"
                    className="w-full h-auto max-h-[500px] xl:max-h-[600px] object-contain rounded-lg border"
                  />
                  <div className="flex gap-2">
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => {
                        const link = document.createElement('a');
                        link.href = resultImageUrl;
                        link.download = 'tryon-result.jpg';
                        link.click();
                      }}
                    >
                      <Download className="mr-2 h-4 w-4" />
                      下载图片
                    </Button>
                  </div>
                </div>
              ) : (
                <div className="flex flex-col items-center justify-center h-full min-h-[400px] xl:min-h-[600px] text-gray-500">
                  <Sparkles className="h-16 w-16 xl:h-20 xl:w-20 mb-6 opacity-50" />
                  <p className="text-xl xl:text-2xl font-medium mb-2">暂无试衣结果</p>
                  <p className="text-sm xl:text-base text-center max-w-sm">配置图片并开始虚拟试衣以查看结果</p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}

export default TryOnPage;
